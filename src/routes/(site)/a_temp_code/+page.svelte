<script lang="ts">
    import {
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Table,
        Spinner,
    } from 'flowbite-svelte';
    import { formatTimestamp } from '$lib/utils';
    import type { PageData } from './$types';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';

    export let data: PageData;
    $: ({tickets, users, statuses, error} = data);
</script>

<div class="flex h-screen">
    {#if error}
        <p class="text-red-500">{error}</p>
    {:else if !tickets}
        <div class="flex justify-center items-center h-32">
            <Spinner size="lg" />
        </div>
    {:else if tickets.length === 0}
        <p>No tickets found.</p>
    {:else}
        <Table shadow>
            <TableHead>
                <TableHeadCell>ID</TableHeadCell>
                <TableHeadCell>Customer name</TableHeadCell>
                <TableHeadCell>Owner</TableHeadCell>
                <TableHeadCell>Status</TableHeadCell>
                <TableHeadCell>Main Interface</TableHeadCell>
                <TableHeadCell>Intent</TableHeadCell>
                <TableHeadCell>Created On</TableHeadCell>
                <TableHeadCell>Updated On</TableHeadCell>
                <TableHeadCell>Actions</TableHeadCell>
            </TableHead>
            <TableBody>
                {#each tickets as ticket}
                    <TableBodyRow>
                        <TableBodyCell>
                            <a href="/monitoring/{ticket.id}" class="text-blue-600 hover:underline">
                                {ticket.id}
                            </a>
                        </TableBodyCell>
                        <TableBodyCell>{ticket.customer.name}</TableBodyCell>
                        <TableBodyCell>{ticket.owner.username}</TableBodyCell>
                        <TableBodyCell>{ticket.status}</TableBodyCell>
                        <TableBodyCell>{ticket.customer.main_interface}</TableBodyCell>
                        <TableBodyCell>{ticket.message_intends}</TableBodyCell>
                        <TableBodyCell>{formatTimestamp(ticket.created_on)}</TableBodyCell>
                        <TableBodyCell>{formatTimestamp(ticket.updated_on)}</TableBodyCell>
                        <TableBodyCell>
                            <TransferTicketOwner {ticket} {users} />
                        </TableBodyCell>
                        <TableBodyCell>
                            <ChangeTicketStatus {ticket} {statuses} ticket_topics={[]} />
                        </TableBodyCell>
                    </TableBodyRow>
                {/each}
            </TableBody>
        </Table>
    {/if}
</div>